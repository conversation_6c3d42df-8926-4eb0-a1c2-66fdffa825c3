<?php
/**
 * Security Validation Functions
 */

class SecurityValidator {
    
    /**
     * Validate donation amount
     */
    public static function validateAmount($amount) {
        if (!is_numeric($amount)) {
            return ['valid' => false, 'error' => 'Amount must be numeric'];
        }
        
        $amount = (float)$amount;
        if ($amount < 1) {
            return ['valid' => false, 'error' => 'Minimum donation amount is ₹1'];
        }
        
        if ($amount > 5000000) { // Max 50 lakh
            return ['valid' => false, 'error' => 'Maximum donation amount is ₹50,00,000'];
        }
        
        return ['valid' => true, 'amount' => $amount];
    }
    
    /**
     * Validate PAN number
     */
    public static function validatePAN($pan) {
        if (empty($pan)) {
            return ['valid' => true]; // PAN is optional
        }
        
        $pan = strtoupper(trim($pan));
        if (!preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $pan)) {
            return ['valid' => false, 'error' => 'Invalid PAN format. Use format: **********'];
        }
        
        return ['valid' => true, 'pan' => $pan];
    }
    
    /**
     * Validate phone number
     */
    public static function validatePhone($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone); // Remove non-digits
        
        if (strlen($phone) < 10 || strlen($phone) > 15) {
            return ['valid' => false, 'error' => 'Phone number must be 10-15 digits'];
        }
        
        // Indian mobile number validation
        if (strlen($phone) == 10 && !preg_match('/^[6-9][0-9]{9}$/', $phone)) {
            return ['valid' => false, 'error' => 'Invalid Indian mobile number'];
        }
        
        return ['valid' => true, 'phone' => $phone];
    }
    
    /**
     * Validate email
     */
    public static function validateEmail($email) {
        $email = trim($email);
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'error' => 'Invalid email format'];
        }
        
        if (strlen($email) > 254) {
            return ['valid' => false, 'error' => 'Email too long'];
        }
        
        return ['valid' => true, 'email' => $email];
    }
    
    /**
     * Validate name
     */
    public static function validateName($name) {
        $name = trim($name);
        if (strlen($name) < 2) {
            return ['valid' => false, 'error' => 'Name must be at least 2 characters'];
        }
        
        if (strlen($name) > 100) {
            return ['valid' => false, 'error' => 'Name too long (max 100 characters)'];
        }
        
        if (!preg_match('/^[a-zA-Z\s\.\-\']+$/', $name)) {
            return ['valid' => false, 'error' => 'Name contains invalid characters'];
        }
        
        return ['valid' => true, 'name' => $name];
    }
    
    /**
     * Validate address
     */
    public static function validateAddress($address) {
        if (empty($address)) {
            return ['valid' => true]; // Address is optional
        }
        
        $address = trim($address);
        if (strlen($address) > 500) {
            return ['valid' => false, 'error' => 'Address too long (max 500 characters)'];
        }
        
        return ['valid' => true, 'address' => $address];
    }
    
    /**
     * Validate message
     */
    public static function validateMessage($message) {
        if (empty($message)) {
            return ['valid' => true]; // Message is optional
        }
        
        $message = trim($message);
        if (strlen($message) > 1000) {
            return ['valid' => false, 'error' => 'Message too long (max 1000 characters)'];
        }
        
        return ['valid' => true, 'message' => $message];
    }
    
    /**
     * Rate limiting check
     */
    public static function checkRateLimit($ip, $action = 'donation', $maxAttempts = 5, $timeWindow = 300) {
        $logFile = __DIR__ . '/../logs/rate-limit.log';
        $now = time();
        
        // Read existing attempts
        $attempts = [];
        if (file_exists($logFile)) {
            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                $data = json_decode($line, true);
                if ($data && $data['ip'] === $ip && $data['action'] === $action && ($now - $data['time']) < $timeWindow) {
                    $attempts[] = $data;
                }
            }
        }
        
        if (count($attempts) >= $maxAttempts) {
            return ['allowed' => false, 'error' => 'Too many attempts. Please try again later.'];
        }
        
        // Log this attempt
        $logEntry = json_encode(['ip' => $ip, 'action' => $action, 'time' => $now]) . "\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        return ['allowed' => true];
    }
    
    /**
     * Sanitize output for display
     */
    public static function sanitizeOutput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeOutput'], $data);
        }
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}
?>
