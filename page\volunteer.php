<?php
$page_title = "Volunteer | Unnyanpath Foundation - Get Involved";
$meta_description = "Join Unnyanpath Foundation as a volunteer and help make a difference in the lives of children and communities across Uttar Pradesh.";

if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];
?>
<!-- Page Header Start -->
    <div class="page-header parallaxie">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12">
                    <!-- Page Header Box Start -->
                    <div class="page-header-box">
                        <h1 class="text-anime-style-2" data-cursor="-opaque"><span>Volunteer</span></h1>
                        <nav class="wow fadeInUp">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?= base_url ?>home">home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">volunteer</li>
                            </ol>
                        </nav>
                    </div>
                    <!-- Page Header Box End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Page Volunteer Form Start -->
    <div class="page-donation">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="donation-box">
                        <div class="section-title">
                            <h3 class="wow fadeInUp">Join Us</h3>
                            <h2 class="text-anime-style-2" data-cursor="-opaque">Become a <span>Volunteer</span></h2>
                            <p class="wow fadeInUp" data-wow-delay="0.2s">
                                Volunteering at Unnyanpath Foundation is a chance to create lasting impact. Help us bring quality education and support to communities in need. Join hands with us and be the change!
                            </p>
                        </div>

                        <!-- Volunteer Form -->
                        <div class="donate-form campaign-donate-form">
                            <form id="volunteerForm">
                                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                                <div class="row">
                                    <!-- Full Name -->
                                    <div class="form-group col-md-6 mb-4">
                                        <input type="text" name="full_name" class="form-control" placeholder="Full Name" required>
                                    </div>

                                    <!-- Email -->
                                    <div class="form-group col-md-6 mb-4">
                                        <input type="email" name="email" class="form-control" placeholder="Email" required>
                                    </div>

                                    <!-- Age -->
                                    <div class="form-group col-md-6 mb-4">
                                        <input type="number" name="age" class="form-control" placeholder="Age" min="12" required>
                                    </div>

                                    <!-- Gender -->
                                    <div class="form-group col-md-6 mb-4">
                                        <select name="gender" class="form-control" required>
                                            <option value="" disabled selected>Select Gender</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Non-binary">Non-binary</option>
                                            <option value="Prefer not to say">Prefer not to say</option>
                                        </select>
                                    </div>

                                    <!-- Mobile No -->
                                    <div class="form-group col-md-6 mb-4">
                                        <input type="tel" name="phone" class="form-control" placeholder="Mobile No." required>
                                    </div>

                                    <!-- Complete Address -->
                                    <div class="form-group col-md-6 mb-4">
                                        <textarea name="address" class="form-control" rows="1" placeholder="Complete Address" required></textarea>
                                    </div>

                                    <!-- Areas of Help (Checkboxes) -->
                                    <div class="form-group col-md-12 mb-4">
                                        <label><strong>What can you help with?</strong></label><br>
                                        <label><input type="checkbox" name="areas_of_help[]" value="Teaching"> Teaching</label><br>
                                        <label><input type="checkbox" name="areas_of_help[]" value="Fundraising"> Fundraising</label><br>
                                        <label><input type="checkbox" name="areas_of_help[]" value="Content Writing"> Content Writing</label><br>
                                        <label><input type="checkbox" name="areas_of_help[]" value="Event Support"> Event Support</label><br>
                                        <label><input type="checkbox" name="areas_of_help[]" value="Technology Support"> Technology Support</label><br>
                                        <label><input type="checkbox" name="areas_of_help[]" value="Other"> Other</label>
                                    </div>

                                    <!-- Preferred Time -->
                                    <div class="form-group col-md-12 mb-4">
                                        <label><strong>What timings would work for you?</strong></label>
                                        <select name="timing" class="form-control" required>
                                            <option value="" disabled selected>Select availability</option>
                                            <option value="Weekdays Morning">Weekdays Morning</option>
                                            <option value="Weekdays Evening">Weekdays Evening</option>
                                            <option value="Weekends Only">Weekends Only</option>
                                            <option value="Flexible">Flexible</option>
                                        </select>
                                    </div>

                                    <!-- Why volunteer -->
                                    <div class="form-group col-md-12 mb-5">
                                        <textarea name="message" class="form-control" rows="4" placeholder="Why do you want to volunteer?" required></textarea>
                                    </div>

                                    <!-- reCAPTCHA Widget -->
                                    <div class="form-group col-md-12 mb-3">
                                        <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <button type="submit" class="btn-default"><span>Submit</span></button>
                                    </div>
                                </div>
                            </form>

                        </div>
                        <!-- Volunteer Form End -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Page Volunteer Form End -->

    <script>
        document.getElementById("volunteerForm").addEventListener("submit", function(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = 'Sending...';
            submitBtn.disabled = true;

            // Get reCAPTCHA response and append to formData
            var recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse) {
                Swal.fire({
                    icon: "error",
                    title: "reCAPTCHA Required",
                    text: "Please complete the reCAPTCHA.",
                    background: "#F8F8F8",
                    color: "#224520",
                    confirmButtonColor: "#f15e25",
                });
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
                return;
            }
            formData.append('g-recaptcha-response', recaptchaResponse);

            fetch("../webhook/volunteer-webhook.php", {
                    method: "POST",
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: "success",
                            title: "Thank you!",
                            text: "Your volunteer request has been submitted successfully.",
                            background: "#F8F8F8",
                            color: "#224520",
                            confirmButtonColor: "#f15e25",
                        });
                        form.reset();
                        grecaptcha.reset();
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: data.error || "Submission failed. Please try again.",
                            background: "#F8F8F8",
                            color: "#224520",
                            confirmButtonColor: "#f15e25",
                        });
                    }
                })
                .catch(() => {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Submission failed. Please try again.",
                        background: "#F8F8F8",
                        color: "#224520",
                        confirmButtonColor: "#f15e25",
                    });
                })
                .finally(() => {
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                });
        });
    </script>

    <!-- Load Google reCAPTCHA JS -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>