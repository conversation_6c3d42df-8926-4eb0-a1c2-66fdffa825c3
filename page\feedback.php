<?php
$page_title = "Feedback | Unnyanpath Foundation - Share Your Thoughts";
$meta_description = "We value your feedback! Share your thoughts and experiences with Unnyanpath Foundation to help us improve our programs and services.";

if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];
?>
<!-- Page Header Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-12">
                <!-- Page Header Box Start -->
                <div class="page-header-box">
                    <h1 class="text-anime-style-2" data-cursor="-opaque"><span>Feedback</span></h1>
                    <nav class="wow fadeInUp">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?= base_url ?>home">home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">feedback</li>
                        </ol>
                    </nav>
                </div>
                <!-- Page Header Box End -->
            </div>
        </div>
    </div>
</div>
<!-- Page Header End -->

<!-- Page Feedback Form Start -->
<div class="page-donation">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="donation-box">
                    <div class="section-title">
                        <h3 class="wow fadeInUp">We Value Your Feedback</h3>
                        <h2 class="text-anime-style-2" data-cursor="-opaque">Share Your <span>Feedback</span></h2>
                        <p class="wow fadeInUp" data-wow-delay="0.2s">
                            Your feedback helps us improve and serve you better. Please fill out the form below to share your experience with UnnyanPath Foundation.
                        </p>
                    </div>

                    <div class="donate-form campaign-donate-form">
                        <form id="feedbackForm">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                            <div class="row">

                                <!-- Full Name -->
                                <div class="form-group col-md-6 mb-4">
                                    <input type="text" name="full_name" class="form-control" placeholder="Full Name" required>
                                </div>

                                <!-- Email -->
                                <div class="form-group col-md-6 mb-4">
                                    <input type="email" name="email" class="form-control" placeholder="Email" required>
                                </div>

                                <!-- Feedback Category -->
                                <div class="form-group col-md-6 mb-4">
                                    <label><strong>Feedback Category</strong></label>
                                    <select name="category" class="form-control" required>
                                        <option value="" disabled selected>Select category</option>
                                        <option value="Website">Website</option>
                                        <option value="Volunteer Experience">Volunteer Experience</option>
                                        <option value="Donation Process">Donation Process</option>
                                        <option value="Event Feedback">Event Feedback</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>

                                <!-- Rating -->
                                <div class="form-group col-md-6 mb-4">
                                    <label><strong>How would you rate your experience?</strong></label>
                                    <select name="rating" class="form-control" required>
                                        <option value="" disabled selected>Select rating</option>
                                        <option value="5">Excellent</option>
                                        <option value="4">Very Good</option>
                                        <option value="3">Good</option>
                                        <option value="2">Fair</option>
                                        <option value="1">Poor</option>
                                    </select>
                                </div>

                                <!-- Message -->
                                <div class="form-group col-md-12 mb-5">
                                    <textarea name="message" class="form-control" rows="4" placeholder="Your feedback..." required></textarea>
                                </div>

                                <!-- Would you recommend us -->
                                <div class="form-group col-md-6 mb-4">
                                    <label><strong>Would you recommend us to others?</strong></label><br>
                                    <label><input type="radio" name="recommend" value="Yes" required> Yes</label> &nbsp;&nbsp;
                                    <label><input type="radio" name="recommend" value="No" required> No</label>
                                </div>

                                <!-- reCAPTCHA Widget -->
                                <div class="form-group col-md-12 mb-3">
                                    <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                                </div>
                                <div class="form-group col-md-12">
                                    <button type="submit" class="btn-default"><span>Submit</span></button>
                                </div>

                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<!-- Page Feedback Form End -->

<!-- Load Google reCAPTCHA JS -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<script>
    document.getElementById("feedbackForm").addEventListener("submit", function(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = 'Sending...';
        submitBtn.disabled = true;

        // Get reCAPTCHA response and append to formData
        var recaptchaResponse = grecaptcha.getResponse();
        if (!recaptchaResponse) {
            Swal.fire({
                icon: "error",
                title: "reCAPTCHA Required",
                text: "Please complete the reCAPTCHA.",
                background: "#F8F8F8",
                color: "#224520",
                confirmButtonColor: "#f15e25",
            });
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            return;
        }
        formData.append('g-recaptcha-response', recaptchaResponse);

        fetch("../webhook/feedback-webhook.php", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: "success",
                        title: "Thank you!",
                        text: "Your feedback was submitted successfully.",
                        background: "#F8F8F8",
                        color: "#224520",
                        confirmButtonColor: "#f15e25",
                    });
                    form.reset();
                    grecaptcha.reset();
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: data.error || "Submission failed. Please try again.",
                        background: "#F8F8F8",
                        color: "#224520",
                        confirmButtonColor: "#f15e25",
                    });
                }
            })
            .catch(() => {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Submission failed. Please try again.",
                    background: "#F8F8F8",
                    color: "#224520",
                    confirmButtonColor: "#f15e25",
                });
            })
            .finally(() => {
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            });
    });
</script>