<?php 
require_once('include/secure-config.php');

// Security: Define allowed pages to prevent directory traversal
$allowed_pages = [
    'home', 'about', 'contact', 'donate', 'volunteer', 'feedback',
    'campaigns', 'what-we-do', 'team', 'testimonials', 'media',
    'partnerships', 'flagship-program', 'student-stories',
    'volunteer-leadership', 'faqs', 'privacy-policy', 'terms-conditions',
    'amrish-kumar', 'kamal<PERSON>-singh', 'shubham-sahu', 'video',
    'coming-soon', '404'
];

// Determine which page to load with security validation
$page = isset($_GET['p']) ? $_GET['p'] : 'home';

// Security: Sanitize page parameter
$page = preg_replace('/[^a-zA-Z0-9\-]/', '', $page);

// Security: Check if page is in allowed list
if (!in_array($page, $allowed_pages)) {
    $page = '404';
}

$folder = "page/";
$file = glob($folder . "*.php");
$file_name = $folder . $page . ".php";

// Set default SEO values for missing/404 or if not set by the page
if (!in_array($file_name, $file)) {
    $page_title = "404 Not Found | Unnyanpath Foundation";
    $meta_description = "The page you are looking for does not exist. Discover Unnyanpath Foundation's mission and explore our real stories of change in Uttar Pradesh.";
} else {
    // Optionally, set a generic default for pages that don't set their own
    $page_title = isset($page_title) ? $page_title : null;
    $meta_description = isset($meta_description) ? $meta_description : null;
}

// Security: Add security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
if (IS_PRODUCTION) {
    header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
}

// Security: Content Security Policy
$csp = "default-src 'self'; ";
$csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://checkout.razorpay.com https://www.googletagmanager.com https://cdn.jsdelivr.net; ";
$csp .= "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; ";
$csp .= "font-src 'self' data: https://fonts.gstatic.com https://cdn.jsdelivr.net; ";
$csp .= "img-src 'self' data: https:; ";
$csp .= "connect-src 'self' https://api.razorpay.com https://www.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://docs.google.com; ";
$csp .= "frame-src https://www.google.com https://checkout.razorpay.com https://api.razorpay.com; ";
$csp .= "media-src 'self';";
header("Content-Security-Policy: $csp");
?>
<!DOCTYPE html>
<html lang="en" itemscope itemtype="https://schema.org/WebPage">
<?php include('include/header.php') ?>

<body>
      <!-- Google tag (gtag.js) -->
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-SXN3X8JX5W"></script>
      <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                  dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-SXN3X8JX5W');
      </script>
      
      <!-- Preloader Start -->
      <div class="preloader" role="status" aria-label="Page loading">
            <div class="loading-container">
                  <div class="loading"></div>
                  <div id="loading-icon"><img src="<?= base_url ?>static/images/logo/loader.svg" alt="Loading..."></div>
            </div>
      </div>
      <!-- Preloader End -->
      <main role="main" id="main-content" aria-label="Main Content">
            <?php 
            include('include/top_nav_bar.php');

            if (in_array($file_name, $file)) {
                  include($file_name);
            } else {
                  include 'page/404.php';
            }
            include('include/footer.php')
            ?>
      </main>
</body>

</html>
