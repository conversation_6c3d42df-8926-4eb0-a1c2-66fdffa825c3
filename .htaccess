# Hostinger-Optimized Security Configuration
# UnnyanPath Foundation Website

# Enable URL rewriting
RewriteEngine On

# Force HTTPS redirect (Hostinger SSL)
RewriteCond %{HTTPS} off [OR]
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteRule ^ https://unnyanpathfoundation.in%{REQUEST_URI} [R=301,L]

# Security Headers (Hostinger compatible)
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Clickjacking protection (SAMEORIGIN for Google reCAPTCHA)
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # HTTPS enforcement (only if HTTPS is available)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Remove server information
    Header unset Server
    Header unset X-Powered-By
    
    # Content Security Policy (relaxed for functionality)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://checkout.razorpay.com https://www.googletagmanager.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; font-src 'self' data: https://fonts.gstatic.com https://cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self' https://api.razorpay.com https://www.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://docs.google.com; frame-src https://www.google.com https://checkout.razorpay.com https://api.razorpay.com; media-src 'self';"
</IfModule>

# Protect sensitive files and directories
<FilesMatch "\.(env|log|ini|conf|bak|sql|git|svn|htaccess|htpasswd)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect sensitive directories from direct access
<DirectoryMatch "(include|logs|vendor)">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# Block access to backup and temporary files
<FilesMatch "\.(bak|backup|old|tmp|temp|swp|~|orig)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to version control files
<FilesMatch "\.(git|svn|hg|bzr)">
    Order allow,deny
    Deny from all
</FilesMatch>

# Allow static assets (images, CSS, JS) to be accessed freely for emails and external use
RewriteCond %{REQUEST_URI} ^/static/
RewriteRule ^(.*)$ - [L]

# Specific rule for email images - allow without referrer check
RewriteCond %{REQUEST_URI} ^/static/images/logo/mail-logo\.(png|jpg|jpeg|svg)$ [NC]
RewriteRule ^(.*)$ - [L]

# Special handling for webhook directory
# Allow POST requests to webhooks
RewriteCond %{REQUEST_METHOD} POST
RewriteCond %{REQUEST_URI} ^/webhook/.*\.php$
RewriteRule ^(.*)$ - [L]

# Block all other access to webhook PHP files
RewriteCond %{REQUEST_METHOD} !POST
RewriteRule ^webhook/.*\.php$ https://unnyanpathfoundation.in/404 [R=302,L]

# Block access to other files in webhook directory
RewriteRule ^webhook/.*\.(?!php$)[^/]+$ https://unnyanpathfoundation.in/404 [R=302,L]

# Disable directory browsing
Options -Indexes

# Disable server-side includes and CGI
Options -Includes -ExecCGI

# Prevent access to PHP configuration files
<Files "php.ini">
    Order allow,deny
    Deny from all
</Files>

# Clean URL rewriting
# Handle URLs like /about to index.php?p=about
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([a-zA-Z0-9\-]+)/?$ index.php?p=$1 [L,QSA]

# Hotlink protection (email-friendly - allow empty referrer for email clients)
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?unnyanpathfoundation\.in [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?google\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?facebook\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?linkedin\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://mail\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://outlook\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://gmail\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yahoo\. [NC]
RewriteRule \.(jpg|jpeg|png|gif|bmp|svg|webp|ico|pdf|mp4|webm)$ https://unnyanpathfoundation.in/static/images/logo/logo.png [R,NC,L]

# Custom error pages
ErrorDocument 400 https://unnyanpathfoundation.in/404
ErrorDocument 401 https://unnyanpathfoundation.in/404
ErrorDocument 403 https://unnyanpathfoundation.in/404
ErrorDocument 404 https://unnyanpathfoundation.in/404
ErrorDocument 500 https://unnyanpathfoundation.in/404

# Performance optimization - Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Browser caching for static files
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # Documents
    ExpiresByType application/pdf "access plus 1 month"
    
    # Media
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType video/webm "access plus 1 month"
    
    # Default
    ExpiresDefault "access plus 2 days"
</IfModule>

# Security: Prevent access to sensitive files by file extension
<FilesMatch "\.(md|txt|log|conf|ini|bak|sql|sh|py|rb|pl)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Allow robots.txt and sitemap.xml
<Files "robots.txt">
    Order allow,deny
    Allow from all
</Files>

<Files "sitemap.xml">
    Order allow,deny
    Allow from all
</Files>

# Rate limiting (basic protection)
# Note: Advanced rate limiting requires server-level configuration
<IfModule mod_rewrite.c>
    # Block requests with suspicious patterns
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} [\[\]\(\)<>;?\*=] [NC,OR]
    RewriteCond %{QUERY_STRING} [\'<>\|] [NC,OR]
    RewriteCond %{QUERY_STRING} %[0-9A-F][0-9A-F] [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>
